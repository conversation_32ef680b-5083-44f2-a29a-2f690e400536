import SwiftUI

/// 用户偏好主页面 - 简洁美观的设计
///
/// 包含主要选项：
/// - Do Not Eat (过敏与严格禁忌)
/// - Dietary Restrictions (饮食限制)
/// - Family Size (家庭规模)
/// - Kitchen Equipment (厨房设备)
struct PreferencesEditView: View {

    // MARK: - Environment Objects

    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss

    // MARK: - Services

    @StateObject private var userProfileService = UserProfileService.shared
    @StateObject private var networkService = OptimizedNetworkService.shared

    // MARK: - Navigation State

    @State private var showingDoNotEat = false
    @State private var showingDietaryRestrictions = false
    @State private var showingFamilySize = false
    @State private var showingEquipment = false
#if DEBUG
    @State private var showingLegacyStrict = false
    @State private var showingLegacyAllergies = false
#endif
    @State private var showingSignOutAlert = false

    // MARK: - Preference Management State

    @State private var isSavingPreferences = false
    @State private var saveError: Error?
    @State private var showingSaveError = false
    @State private var showingSaveSuccess = false
    @State private var pendingPreferences: UserPreferences?

    // MARK: - Current Preferences

    private var preferences: UserPreferences {
        pendingPreferences ?? authService.userPreferences ?? UserPreferences.createDefault(for: authService.currentUser?.uid ?? "")
    }

    // MARK: - Body

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {

                // Network Status Banner (shows when offline/reconnecting)
                NetworkStatusBanner(status: networkStatusForBanner)

                ScrollView(.vertical) {
                    VStack(spacing: 24) {

                        // Header
                        headerSection

                        // Sync Status Indicator
                        if shouldShowSyncStatus {
                            SyncStatusView(
                                status: currentSyncStatus,
                                onRetry: {
                                    Task {
                                        if let pending = pendingPreferences {
                                            await savePreferences(pending)
                                        }
                                    }
                                }
                            )
                            .padding(.horizontal, 20)
                        }

                        // Main Options
                        VStack(spacing: 16) {

                            // Unified Do Not Eat entry
                            preferenceCard(
                                title: "Do Not Eat",
                                subtitle: "Allergies & strict exclusions",
                                icon: "fork.knife.circle.fill",
                                iconColor: .orange,
                                count: doNotEatCount,
                                action: { showingDoNotEat = true }
                            )

                            // Dietary Restrictions
                            preferenceCard(
                                title: "Dietary Restrictions",
                                subtitle: "Your dietary preferences",
                                icon: "leaf.fill",
                                iconColor: .green,
                                count: preferences.dietaryRestrictions.count,
                                action: { showingDietaryRestrictions = true }
                            )

                            // Family Size
                            preferenceCard(
                                title: "Family Members",
                                subtitle: "Adults and kids",
                                icon: "person.2.fill",
                                iconColor: .blue,
                                count: preferences.numberOfAdults + preferences.numberOfKids,
                                action: { showingFamilySize = true }
                            )

                            // Kitchen Equipment
                            preferenceCard(
                                title: "Kitchen Equipment",
                                subtitle: "Cookware & appliances",
                                icon: "cooktop.fill",
                                iconColor: .purple,
                                count: preferences.equipmentOwned.count,
                                action: { showingEquipment = true }
                            )
#if DEBUG
                            // Legacy editors kept for debugging
                            preferenceCard(
                                title: "Legacy Strict Exclusions",
                                subtitle: "Debug-only editor",
                                icon: "xmark.circle",
                                iconColor: .red,
                                count: preferences.strictExclusions.count,
                                action: { showingLegacyStrict = true }
                            )

                            preferenceCard(
                                title: "Legacy Allergies",
                                subtitle: "Debug-only editor",
                                icon: "exclamationmark.triangle",
                                iconColor: .orange,
                                count: preferences.allergiesIntolerances.count,
                                action: { showingLegacyAllergies = true }
                            )
#endif
                        }

                        // Settings entry (local-only)
                        NavigationLink {
                            SettingsView()
                        } label: {
                            HStack(spacing: 16) {
                                // Icon
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.gray.opacity(0.1))
                                    .frame(width: 50, height: 50)
                                    .overlay(
                                        Image(systemName: "gearshape.fill")
                                            .font(.system(size: 22, weight: .medium))
                                            .foregroundStyle(.gray)
                                    )

                                // Content
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Settings")
                                        .font(.headline)
                                        .foregroundStyle(.primary)
                                        .multilineTextAlignment(.leading)

                                    Text("Local app settings")
                                        .font(.subheadline)
                                        .foregroundStyle(.secondary)
                                        .multilineTextAlignment(.leading)
                                }

                                Spacer()

                                Image(systemName: "chevron.right")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundStyle(.tertiary)
                            }
                            .padding(20)
                            .background(Color(.systemBackground))
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
                        }
                        .buttonStyle(.plain)

                            // Privacy & Telemetry
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 12) {
                                    Image(systemName: "waveform.path.ecg")
                                        .font(.title3)
                                        .foregroundStyle(.purple)
                                    Text("Analytics & Telemetry")
                                        .font(.headline)
                                }
                                Toggle(isOn: Binding(
                                    get: { TelemetryService.isOptedIn },
                                    set: { TelemetryService.setUserConsent($0) }
                                )) {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("Share anonymous usage analytics")
                                        Text("Helps us improve scanning performance and reliability. No personal data is collected.")
                                            .font(.footnote)
                                            .foregroundStyle(.secondary)
                                    }
                                }
                                .tint(.purple)
                            }
                            .padding(16)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(RoundedRectangle(cornerRadius: 12).fill(Color(.secondarySystemBackground)))

                        .padding(.horizontal, 20)

                        // Sign Out Section
                        signOutSection

                        Spacer(minLength: 40)
                    }
                    .padding(.vertical, 20)
                }
                .background(Color(.systemGroupedBackground))
            }
            .navigationTitle("Preferences")
            .navigationBarTitleDisplayMode(.large)
        }
        .sheet(isPresented: $showingDoNotEat) {
            DoNotEatView()
        }
        .sheet(isPresented: $showingDietaryRestrictions) {
            DietaryRestrictionsEditView(
                preferences: preferences,
                onSave: { updatedPreferences in
                    await savePreferences(updatedPreferences)
                }
            )
        }
        .sheet(isPresented: $showingFamilySize) {
            FamilySizeEditView(
                preferences: preferences,
                onSave: { updatedPreferences in
                    await savePreferences(updatedPreferences)
                }
            )
        }
        .sheet(isPresented: $showingEquipment) {
            EquipmentChipsView(
                initialSelection: preferences.equipmentOwned,
                onSave: { selection in
                    Task {
                        var updated = preferences
                        updated.equipmentOwned = selection
                        await savePreferences(updated)
                    }
                }
            )
        }
#if DEBUG
        .sheet(isPresented: $showingLegacyStrict) {
            StrictExclusionsEditView(
                preferences: preferences,
                onSave: { updatedPreferences in
                    await savePreferences(updatedPreferences)
                }
            )
        }
        .sheet(isPresented: $showingLegacyAllergies) {
            AllergiesIntolerancesEditView(
                preferences: preferences,
                onSave: { updatedPreferences in
                    await savePreferences(updatedPreferences)
                }
            )
        }
#endif
        .alert("Sign Out", isPresented: $showingSignOutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Sign Out", role: .destructive) {
                signOut()
            }
        } message: {
            Text("Are you sure you want to sign out? Your preferences will be saved.")
        }
        .alert("Save Error", isPresented: $showingSaveError) {
            Button("OK") { }
            Button("Retry") {
                Task {
                    if let pending = pendingPreferences {
                        await savePreferences(pending)
                    }
                }
            }
        } message: {
            Text(saveError?.localizedDescription ?? "Failed to save preferences")
        }
        .onReceive(NotificationCenter.default.publisher(for: .networkReconnected)) { _ in
            // Handle reconnection - could trigger sync of pending changes
            print("🔄 Network reconnected - checking for pending sync operations")
        }
    }

    // MARK: - Computed Properties for UI State

    private var networkStatusForBanner: NetworkStatusBanner.NetworkStatus {
        switch networkService.networkStatus {
        case .online:
            return .online
        case .offline:
            return .offline
        case .reconnecting:
            return .reconnecting
        }
    }

    private var currentSyncStatus: SyncStatusView.SyncStatus {
        if isSavingPreferences {
            return .syncing
        } else if showingSaveSuccess {
            return .success
        } else if let error = saveError {
            return .failure(error)
        } else if !networkService.isOnline {
            return .offline
        } else {
            return userProfileService.getCurrentSyncStatus()
        }
    }

    private var shouldShowSyncStatus: Bool {
        return isSavingPreferences ||
               showingSaveSuccess ||
               saveError != nil ||
               !networkService.isOnline ||
               (userProfileService.getCurrentSyncStatus() != .idle)
    }

    private var doNotEatCount: Int {
        preferences.allergiesIntolerances.count +
        preferences.strictExclusions.count +
        preferences.customStrictExclusions.count
    }

    // MARK: - Header Section

    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "person.crop.circle.fill")
                .font(.system(size: 60))
                .foregroundStyle(.blue.gradient)

            Text("Customize Your Experience")
                .font(.title2.weight(.semibold))
                .foregroundStyle(.primary)

            Text("Set your preferences to get personalized recipe recommendations")
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
        }
        .padding(.bottom, 8)
    }

    // MARK: - Sign Out Section

    @ViewBuilder
    private var signOutSection: some View {
        VStack(spacing: 16) {
            // User info
            if let user = authService.currentUser {
                VStack(spacing: 8) {
                    Text("Signed in as")
                        .font(.caption)
                        .foregroundStyle(.secondary)

                    Text(user.email ?? authService.userDisplayName)
                        .font(.subheadline.weight(.medium))
                        .foregroundStyle(.primary)
                }
            }

            // Connection status info
            HStack(spacing: 8) {
                Circle()
                    .fill(networkService.isOnline ? .green : .orange)
                    .frame(width: 8, height: 8)

                Text(networkService.isOnline ? "Connected" : "Working offline")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }

            // Sign out button
            Button("Sign Out") {
                showingSignOutAlert = true
            }
            .foregroundStyle(.red)
            .font(.body.weight(.medium))
            .padding(.horizontal, 32)
            .padding(.vertical, 12)
            .background(.red.opacity(0.1), in: RoundedRectangle(cornerRadius: 12))
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
    }

    // MARK: - Preference Card Component

    @ViewBuilder
    private func preferenceCard(
        title: String,
        subtitle: String,
        icon: String,
        iconColor: Color,
        count: Int,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // Icon
                RoundedRectangle(cornerRadius: 12)
                    .fill(iconColor.opacity(0.1))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: icon)
                            .font(.system(size: 22, weight: .medium))
                            .foregroundStyle(iconColor)
                    )

                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundStyle(.primary)
                        .multilineTextAlignment(.leading)

                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                // Count Badge & Arrow
                HStack(spacing: 12) {
                    if count > 0 {
                        Text("\(count)")
                            .font(.caption.weight(.semibold))
                            .foregroundStyle(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(iconColor, in: Capsule())
                    }

                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundStyle(.tertiary)
                }
            }
            .padding(20)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        }
        .buttonStyle(.plain)
        .disabled(isSavingPreferences)
        .opacity(isSavingPreferences ? 0.6 : 1.0)
    }

    // MARK: - Preference Management Methods with Offline Support

    @MainActor
    private func savePreferences(_ updatedPreferences: UserPreferences) async {
        guard let userId = authService.currentUser?.uid else {
            saveError = AuthenticationService.AuthError.userNotFound
            showingSaveError = true
            return
        }

        isSavingPreferences = true
        saveError = nil

        do {
            // Use offline-aware save method
            try await userProfileService.savePreferencesOfflineAware(
                updatedPreferences,
                for: userId,
                showOfflineMessage: true
            )

            // Update local state
            pendingPreferences = updatedPreferences

            // Update AuthenticationService state
            await authService.updatePreferences(updatedPreferences)

            // Show success feedback
            withAnimation(.easeInOut(duration: 0.3)) {
                showingSaveSuccess = true
            }

            // Auto-hide success after delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                withAnimation(.easeOut(duration: 0.3)) {
                    showingSaveSuccess = false
                }
            }

        } catch {
            saveError = error
            showingSaveError = true
        }

        isSavingPreferences = false
    }

    // MARK: - Helper Methods

    private func signOut() {
        do {
            try authService.signOut()
        } catch {
            print("Sign out failed: \(error)")
        }
    }
}

// MARK: - Individual Preference Edit Views (Updated for Task 24)

/// Wrapper view for StrictExclusionsView with save callback
struct StrictExclusionsEditView: View {
    let preferences: UserPreferences
    let onSave: (UserPreferences) async -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var selectedExclusions: Set<StrictExclusion> = []
    @State private var isSaving = false

    var body: some View {
        StrictExclusionsView(
            initialExclusions: preferences.strictExclusions,
            onSave: { exclusions in
                isSaving = true
                var updatedPreferences = preferences
                updatedPreferences.strictExclusions = exclusions
                await onSave(updatedPreferences)
                isSaving = false
                dismiss()
            }
        )
        .disabled(isSaving)
    }
}

/// Wrapper view for DietaryRestrictionsView with save callback
struct DietaryRestrictionsEditView: View {
    let preferences: UserPreferences
    let onSave: (UserPreferences) async -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var isSaving = false

    var body: some View {
        DietaryRestrictionsView(
            initialRestrictions: preferences.dietaryRestrictions,
            onSave: { restrictions in
                isSaving = true
                var updatedPreferences = preferences
                updatedPreferences.dietaryRestrictions = restrictions
                await onSave(updatedPreferences)
                isSaving = false
                dismiss()
            }
        )
        .disabled(isSaving)
    }
}

/// Wrapper view for AllergiesIntolerancesView with save callback
struct AllergiesIntolerancesEditView: View {
    let preferences: UserPreferences
    let onSave: (UserPreferences) async -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var isSaving = false

    var body: some View {
        AllergiesIntolerancesView(
            initialAllergies: preferences.allergiesIntolerances,
            onSave: { allergies in
                isSaving = true
                var updatedPreferences = preferences
                updatedPreferences.allergiesIntolerances = allergies
                await onSave(updatedPreferences)
                isSaving = false
                dismiss()
            }
        )
        .disabled(isSaving)
    }
}

/// Wrapper view for FamilySizeView with save callback
struct FamilySizeEditView: View {
    let preferences: UserPreferences
    let onSave: (UserPreferences) async -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var isSaving = false

    var body: some View {
        FamilySizeView(
            initialAdults: preferences.numberOfAdults,
            initialKids: preferences.numberOfKids,
            onSave: { adults, kids in
                isSaving = true
                var updatedPreferences = preferences
                updatedPreferences.numberOfAdults = max(1, adults)
                updatedPreferences.numberOfKids = max(0, kids)
                updatedPreferences.familySize = updatedPreferences.numberOfAdults + updatedPreferences.numberOfKids
                await onSave(updatedPreferences)
                isSaving = false
                dismiss()
            }
        )
        .disabled(isSaving)
    }
}

// Note: EquipmentEditView intentionally omitted; use EquipmentChipsView for now.

// MARK: - Preview

struct PreferencesEditView_Previews: PreviewProvider {
    static var previews: some View {
        PreferencesEditView()
            .environment(AuthenticationService())
    }
}
