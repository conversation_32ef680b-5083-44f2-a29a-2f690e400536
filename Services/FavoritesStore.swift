import Foundation

@MainActor
final class FavoritesStore {
    static let shared = FavoritesStore()
    private let defaults = UserDefaults.standard
    private let key = "favorites.recipes.v1"
    private let snapshotKey = "favorites.snapshots.v1"

    private let encoder: JSONEncoder = {
        let e = JSONEncoder()
        e.dateEncodingStrategy = .iso8601
        return e
    }()
    private let decoder: JSONDecoder = {
        let d = JSONDecoder()
        d.dateDecodingStrategy = .iso8601
        return d
    }()

    private init() {}

    /// Toggle favorite status. When adding, insert at the front to keep newest-first ordering.
    func toggleFavorite(id: String) {
        var arr = loadArray()
        if let idx = arr.firstIndex(of: id) {
            arr.remove(at: idx)
        } else {
            arr.insert(id, at: 0)
        }
        saveArray(arr)
    }

    func isFavorite(id: String) -> Bool {
        return loadArray().contains(id)
    }

    /// Returns favorite IDs in newest-first order
    func all() -> [String] {
        return loadArray()
    }

    // MARK: - Private

    private func loadArray() -> [String] {
        return (defaults.array(forKey: key) as? [String]) ?? []
    }

    private func saveArray(_ arr: [String]) {
        defaults.set(arr, forKey: key)
    }

    // MARK: - Snapshots
    func saveSnapshot(_ snapshot: FavoriteSnapshot) {
        var map = loadSnapshotMap()
        map[snapshot.id] = snapshot
        saveSnapshotMap(map)
    }

    func snapshot(for id: String) -> FavoriteSnapshot? {
        return loadSnapshotMap()[id]
    }

    func deleteSnapshot(for id: String) {
        var map = loadSnapshotMap()
        map.removeValue(forKey: id)
        saveSnapshotMap(map)
    }

    private func loadSnapshotMap() -> [String: FavoriteSnapshot] {
        guard let data = defaults.data(forKey: snapshotKey) else { return [:] }
        return (try? decoder.decode([String: FavoriteSnapshot].self, from: data)) ?? [:]
    }

    private func saveSnapshotMap(_ map: [String: FavoriteSnapshot]) {
        if let data = try? encoder.encode(map) {
            defaults.set(data, forKey: snapshotKey)
        }
    }
}
