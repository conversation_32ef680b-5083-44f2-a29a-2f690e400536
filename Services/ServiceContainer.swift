import Foundation
import SwiftUI

@Observable
@MainActor
class ServiceContainer {
    static let shared = ServiceContainer()

    // Core services
    let pantryService = PantryService()
    let authenticationService = AuthenticationService()
    let swiftDataStorage = SwiftDataStorageService.shared
    let concurrencyManager = ConcurrencyManager.shared

    // API services (actors)
    let googleVisionService = GoogleVisionAPIService()
    let geminiService = GeminiAPIService()
    let recipeGenerationService: RecipeGenerationServiceProtocol = RecipeGenerationService()

    // Image preparation (actor)
    let imagePreparationService = AsyncImagePreparationService()


    // Telemetry service (opt-in)
    let telemetryService = TelemetryService()

    // Organizer service removed in V5

    private init() {
        // Initialize any necessary configurations
    }

}


// MARK: - App Config (Feature Flags)
enum ProcessingFlowMode { case automated }

@MainActor
struct AppConfig {
    static var processingFlowMode: ProcessingFlowMode = .automated
}
