# Account Settings 重构 PRD

## 背景
- 当前登录用户在 Profile → Preferences 页面最底部看到 “Signed in as”/邮箱/“Sign Out” 区块（参见 `Features/Profile/PreferencesEditView.swift` 中的 `signOutSection`）。
- Settings 页仅包含 Notifications 子页面（`Features/Profile/SettingsView.swift`）。用户无法在一个集中位置管理账号操作或清理本地数据。
- 新需求：把账号相关操作直接整合到 Profile 页面（而非 Settings），并新增 Change Password、Delete Account、Reset Account 功能；Reset Account 需要恢复到初始状态（含清空 pantry、收藏等）。

## 目标
1. 在 Profile 页面内新增 “Account” 区域或子页面，展示当前登录邮箱并提供账号操作。
2. 从 Profile → Preferences 移除原有 “Signed in as”/Sign Out 区块，并在 Profile 内合并呈现账号相关操作，不再通过 Settings 入口。
3. 提供以下操作：Change Password、Reset Account、Delete Account、Sign Out；确保只有适用的操作对用户可用。
4. Reset Account 会清理本地存储的所有用户数据，并把偏好恢复到默认，保持账号仍处于登录状态。
5. 为关键操作增加确认与错误处理，保持现有认证/离线逻辑的一致性。
6. 更新 QA/Analytics 以覆盖新流程。

成功标准：
- 登录用户能在 Profile 页面（Account 区域/子页面）中看到邮箱并执行上述操作。
- Profile 页面整合账号操作，不再需要 Settings 入口；原 Preferences 中不再出现孤立的 signOutSection。
- Reset Account 后，pantry、收藏、计划、历史记录等恢复到初始状态；用户无需重新登录。
- Change Password/Delete Account 处理不同身份提供方（Email vs Apple/Google/匿名）并提供反馈。
- 认证与账户相关实现严格遵循 Firebase Auth iOS Manage Users 文档的 Swift 示例 API 与调用顺序。


## 当前体验 & 痛点
- `PreferencesEditView.signOutSection` 提供邮箱展示与 Sign Out 按钮，且混在偏好设置与 telemetry 区块之间，信息架构混乱。
- `SettingsView` 为静态 `List`，只有“Notifications”入口，无账号/隐私设置。
- 应用内尚未实现更改密码、清空数据或删除账号的 UI；`AuthenticationService` 仅支持 signOut/发送 reset password 邮件。
- 各类用户数据散落在 SwiftData (`SwiftDataStorageService`)、UserDefaults (`FavoritesStore`, `PlanStore`, `TelemetryService`, 各种开关)、磁盘 JSON (`HistoryStorageService`)，缺少统一清理。

## 范围内需求
### 信息架构
- Preferences 主体：保留原有卡片，移除 signOutSection；在 Profile 页面新增 “Account” 区域（或单独子页面），集中展示邮箱与账号操作。
- Settings 根视图：保持仅包含 Notifications（沿用现有 `NotificationsView`），不新增 Account 入口；未来 App 设置可继续在此扩展。

### 合规约束（Firebase Auth iOS Manage Users）
- 规范来源（唯一合格标准）：https://firebase.google.com/docs/auth/ios/manage-users
- 平台约定：实现必须严格遵循该文档的 Swift 示例所示 API、调用顺序与对象模型；如封装自定义方法，需确保最终调用路径与文档行为一致。
- 必须覆盖并遵循的关键 API：
  - 认证状态监听：`Auth.auth().addStateDidChangeListener` / `AuthStateDidChangeListenerHandle`；仅在回调中或 `Auth.auth().currentUser` 非空时执行业务，初始化阶段需等待。
  - 用户属性读取：`Auth.auth().currentUser`, `User.displayName`, `User.email`, `User.photoURL`, `User.isEmailVerified`, `User.providerData`。
  - 资料变更：`User.createProfileChangeRequest()` → 设置字段 → `commitChanges(completion:)`。
  - 邮箱变更：`User.updateEmail(to:)`；完成后调用 `User.reload(completion:)` 同步最新状态，并结合 `User.sendEmailVerification(...)` 提供验证流程。
  - 邮箱验证：`User.sendEmailVerification(completion:)`（或 `sendEmailVerification(beforeUpdatingEmail:actionCodeSettings:completion:)` 版本）；发送前设置语言。
  - 密码变更：`User.updatePassword(to:)`；若遇近期登录要求，使用 `User.reauthenticate(with:completion:)` 后重试。
  - 重置密码邮件：`Auth.auth().sendPasswordReset(withEmail:)`（支持 OAuth 用户触发邮件）。
  - 删除账号：`User.delete(completion:)`；必要时 `User.reauthenticate(with:completion:)` 后再次调用。
  - 提供方信息：遍历 `User.providerData`（`UserInfo.providerID` 等）用于 UI gating 与错误提示。
  - 邮件本地化：`Auth.auth().languageCode` 或 `Auth.auth().useAppLanguage()`。
  - 登出：`Auth.auth().signOut()`（注意 throws，需要捕获并反馈）。
  - 强制刷新：`User.reload(completion:)` 于关键流程后刷新缓存。
  - 凭据生成：`EmailAuthProvider.credential`、`OAuthProvider.credential` 等用于 reauth。
- 错误处理：统一处理 `AuthErrorCode.requiresRecentLogin`（提示重新验证身份 → 生成相应 `AuthCredential` → `user.reauthenticate(with:)` → 成功后重试一次原操作）；其他错误映射到可读提示并允许重试。
### Profile 内的 Account 区域/页面
- Header：
  - 图标（与 profile 保持一致 person.circle）。
  - 显示 “Signed in as” + 邮箱。若 `authService.userEmail` 为空但有 displayName，则展示 displayName 并附 “(no email on file)” 次文案。
  - 若为匿名用户（`isAnonymous == true`）提示“Using temporary guest session”。仅提供 Sign In CTA（跳转现有 SignInView）。匿名用户不显示 Change/Delete/Reset。
- 账号操作列表：使用分组按钮或 `Section`，位于 Profile 内此区域中，并严格遵循 Firebase Auth iOS 文档示例代码的 API/顺序：
  - 资料信息（Account info）：
    1. `Edit Profile`（displayName/photoURL）→ 通过 `user.createProfileChangeRequest()` 设置字段后 `commitChanges(completion:)`。
    2. `Update Email`（需近期登录）→ 调用 `user.updateEmail(to:)`；完成后 `user.reload(completion:)` 并若 `user.isEmailVerified == false` 在 UI 中提示验证。
    3. `Send/Resend Verification Email` → 调用 `user.sendEmailVerification(completion:)`（或带 `ActionCodeSettings` 版本）；发送前设置 `Auth.auth().languageCode` 或 `Auth.auth().useAppLanguage()`。
  - 安全与会话：
    4. `Change Password`（仅当 provider 包含 `password`；OAuth 登录禁用并说明）→ 调用 `user.updatePassword(to:)`；如遇 `AuthErrorCode.requiresRecentLogin`，使用 `user.reauthenticate(with:credential, completion:)`（例如 `EmailAuthProvider.credential`）后重试。未设置密码的邮箱账号提供 `Auth.auth().sendPasswordReset(withEmail:)` 入口。
    5. `Reset Account`（仅本地数据清理，不改动云端账号信息）。
    6. `Delete Account` → 调用 `user.delete(completion:)`；如遇 `AuthErrorCode.requiresRecentLogin`，通过 provider 对应的 `AuthCredential` 执行 `user.reauthenticate(with:)` 后重试。
    7. `Sign Out` → 调用 `Auth.auth().signOut()`（捕获抛出的错误并反馈）。
- 每个操作进入确认或二级流程：
  - Edit Profile：内联表单或二级页编辑 displayName/photoURL，提交时通过 `createProfileChangeRequest` → `commitChanges` 并处理错误反馈。
  - Update Email：二级页输入新邮箱，提交调用 `user.updateEmail(to:)`；成功后调用 `user.reload(completion:)` 并提示去邮箱完成验证；若返回 `AuthErrorCode.requiresRecentLogin`，先 `user.reauthenticate(with:)` 再重试。
  - Send/Resend Verification：点击即调用 `user.sendEmailVerification`，成功后 toast/banner 提示。
  - Change Password：Push 到新表单视图（当前密码、新密码、确认密码）；必要时先构建 `AuthCredential` 调用 `user.reauthenticate(with:)` 后再 `user.updatePassword(to:)`。若用户为 OAuth 登录或未设置密码，显示说明并提供 `Auth.auth().sendPasswordReset(withEmail:)`。
  - Reset Account：弹出确认对话框，明确说明仅清空当前设备上的 pantry/favorites/meal plans/quick history 等数据并恢复偏好设置，云端账号与历史不会被删除；确认后执行数据清理流程（见技术部分），成功后 toast/banner 提示“已重置至初始状态”。如产品评估更适合的文案（例如“Reset Local Data”/“Clear Data on This Device”），需在设计阶段同步确认。
  - Delete Account：二次确认 + 输入 “DELETE” 文本或密码（按 provider）：
    - Email 密码用户：要求输入密码生成 `EmailAuthProvider.credential` 后 reauth。
    - Apple/Google：使用平台提供的登录结果构造 `OAuthProvider.credential` / `ASAuthorization` → `user.reauthenticate(with:)`。
    - 匿名用户：直接允许删除（即丢弃 session 并回到未登录状态）。
    成功后调用 `user.delete(completion:)`，清理本地数据并返回到 Profile Sign-In 视图。
  - Sign Out：沿用现有 confirm alert → `Auth.auth().signOut()`（或封装方法），完成后停留/返回到 Profile。

  - Reauthenticate Flow：实现可复用的重新验证组件（输入密码、Apple/Google 再登录或触发平台凭据）并集中处理 `AuthErrorCode.requiresRecentLogin`，供 Update Email / Change Password / Delete Account 等流程复用，统一错误提示与 loading 状态。

### Reset Account 清理范围
执行顺序建议集中在新 `AccountResetManager`：
1. 暂停 UI 操作（显示 Loading overlay）。
2. Pantry：调用 `SwiftDataStorageService.clearAllIngredients()`，并通过 `PantryService` 刷新内存状态（重载空列表，清除 recentlyAdded）。
3. Favorites：`FavoritesStore` 新增 `clearAll()`，移除 `favorites.recipes.v1` & `favorites.snapshots.v1`。
4. Meal Plans：`PlanStore` 新增 `clearAll()`，移除 `planstore.lastQuick.v1`、`planstore.lastMealPrep.v1`、`planstore.retentionNotice.v1`。
5. Quick History：`HistoryStorageService` 新增 `clearAllQuick()`，删除 index key (`StorageConfiguration.quickHistoryIndexKey`) 与磁盘文件夹 `QuickHistory/`（可在 `DiskStorageManager` 新增删除文件夹能力）。
6. 生成记录/缓存：
   - `recipes.selectedTab`, `UITEST_*` keys 若存在可保留（仅测试）→ 标记为非关键。
   - Telemetry opt-in (`telemetry_opt_in`)、Food expiration toggle (`foodExpirationReminderEnabled`)、suppress 标记 (`expirationSuppressNoonDate`) 重置为默认（false/移除）。
   - Remote config cache (`remote_config_cache_v1`, `remote_config_timestamp_v1`) 可清空，让用户重新走首次体验。
7. 偏好：
   - 通过 `AuthenticationService.updatePreferences(.createDefault(for: uid))` 保存默认结构并调用 `UserProfileService.savePreferences`（若在线）。
   - 清理本地 SwiftData `SavedUserPreferences`（可沿用 `saveUserPreferences` 写入默认）。
8. 清除本地图像缓存：调用 `DiskStorageManager.cleanupExpired` 无法立即清空，需要新方法 `purgeFolder(name:)`。
9. 完成后 dismiss loading，显示成功提示。
10. 若任何步骤失败，显示错误并停止剩余步骤，让用户重试；必要时回滚（至少记录日志）。

### 验证 & 错误处理
- 网络离线：
  - Reset Account 主要操作本地，不依赖网络；FireStore 同步失败时提示“将稍后同步”。
  - 基于 Firebase Auth 的操作（`user.updateEmail(to:)`/`user.updatePassword(to:)`/`user.sendEmailVerification`/`Auth.auth().sendPasswordReset(withEmail:)`/`user.delete`/`Auth.auth().signOut()`）需在线；离线时禁用并提示。
- 近期登录要求：对 `user.updateEmail`/`user.updatePassword`/`user.delete` 返回 `AuthErrorCode.requiresRecentLogin` 时，统一走 reauth 流程：提示重新输入凭据 → `user.reauthenticate(with:)` 成功后自动重试原操作一次（失败则保留错误）。
- 邮件本地化：发送验证/重置邮件前设置 `Auth.auth().languageCode` 或调用 `Auth.auth().useAppLanguage()`。
- 错误映射：将典型错误（弱密码、邮箱已被占用、无效邮箱、凭据无效等）转为可读提示。
- Loading/禁用：敏感操作进行中禁用重复点击，提交期间显示进度。

### Telemetry / Analytics
- 如果用户 opt-in：
  - `account_profile_updated`
  - `account_email_updated`
  - `account_email_verification_sent`
  - `account_password_reset_email_sent`
  - `account_change_password_success`
  - `account_delete_confirmed`
  - `account_reset_confirmed`
  - `account_sign_out`
  参数包含：`provider`、`duration_ms`、`result` (success/failure)、`error_code`、`requires_recent_login` (bool)、`reauth_attempts`、`language_code`。

### 无障碍 & 设计细节
- Profile → Account 区域使用标准 iOS List/Form，支持 Dynamic Type。
- 所有 destructive 操作标红，配合 `role: .destructive`。
- 确认文案双语（英文默认，简体中文本地化在 Base.lproj / zh-Hans.lproj 中补充）。
### 安全性注意
- 按官方文档提示，不要信任用户可控字段（如 `displayName`/`photoURL`）。展示前需进行转义/过滤，避免 XSS；严禁在未转义情况下作为富文本/HTML 注入。


## 非功能 / Out of Scope
- 不改动 Notifications 逻辑本身。
- 不实现多账号切换。
- 不变更 Sign-In 流程或第三方绑定。
- 不处理服务端批量删除数据（Reset 仅影响本地 + Firestore 偏好）。

## 技术实现要点与依赖
- **视图更新**：
  - 更新 `PreferencesEditView` 去除 `signOutSection` 与相关 alert。
  - 在 Profile 中新增 “Account” 区域/入口（例如 `ProfileAccountView` 或 `AccountSection`），承载账号操作。
  - `SettingsView` 保持现状，仅保留 Notifications，不再新增 Account 相关视图。
- **认证与账户操作（严格对齐 Firebase Auth iOS Manage Users）**：
  - 状态监听：通过 `Auth.auth().addStateDidChangeListener` 与 `AuthStateDidChangeListenerHandle` 初始化/保持用户状态；避免在监听外直接使用 `Auth.auth().currentUser` 执行业务逻辑。
  - 门面封装：在 `AuthenticationService` 中暴露与文档一致的方法并透传 `AuthErrorCode`：
    `createProfileChangeRequest`/`commitChanges`, `updateEmail`, `sendEmailVerification`, `updatePassword`, `sendPasswordResetEmail`, `deleteUser`, `reauthenticate`, `signOut`, `reloadUser`。
  - 提供方 gating：读取 `user.providerData`（`UserInfo.providerID`）决定 UI；OAuth 登录显示禁用说明并提供 `Auth.auth().sendPasswordReset(withEmail:)`。
  - 近期登录：统一拦截 `AuthErrorCode.requiresRecentLogin`，触发 `user.reauthenticate(with:)`，成功后自动重试一次原操作（必要时刷新 `user.reload(completion:)`）。
  - 邮件本地化：发送验证/重置邮件前设置 `Auth.auth().languageCode` 或调用 `Auth.auth().useAppLanguage()`。
- **数据清理组件**：
  - 新建 `AccountResetManager`（或放在 `ServiceContainer`）聚合清理逻辑；确保线程安全（SwiftData 操作在主线程）。
  - `FavoritesStore`, `PlanStore`, `HistoryStorageService`, `DiskStorageManager`、`SwiftDataStorageService` 按“Reset Account 清理范围”章节补齐清理 API，并提供统一的 `reset()`/`clearAll()` 接口供 `AccountResetManager` 顺序调用；各步骤需具备独立错误处理与日志，避免一次失败导致整批回滚困难。
- **依赖**：Firebase Auth 已在项目中；Change/Delete 操作需要在 Info.plist 中确保 reCAPTCHA/安全设置已配置（需与后端确认）。开发前需逐项核对 Firebase Console 配置与 Xcode 工程：上传 APNs Auth Key、补齐自定义 URL Schemes（`com.googleusercontent.apps.*`）以及 `FirebaseAppDelegateProxyEnabled` 等要求，确保 reCAPTCHA、推送与凭证回调能够正常工作。

## QA 验收清单
1. 邮箱密码用户：
   - Edit Profile：成功更新 displayName/photoURL；界面展示内容已转义/过滤。
   - Update Email：通过 `user.updateEmail(to:)` 可成功更新；若遇 `AuthErrorCode.requiresRecentLogin`，弹出 reauth 并在成功后自动重试一次；`user.isEmailVerified == false` 时页面展示未验证状态与重发入口，成功后 `user.reload(completion:)` 刷新。
   - Verification Email：`user.sendEmailVerification` 成功发送；邮件语言跟随 `Auth.auth().languageCode`/`Auth.auth().useAppLanguage()`。
   - Change Password：`user.updatePassword(to:)` 成功修改；弱密码/错误凭据给出可读提示；OAuth 用户改密入口禁用并提供 `Auth.auth().sendPasswordReset(withEmail:)`。
   - Reset Account：本地数据按章节完全清理，登录态仍在。
   - Delete Account：`user.delete` 成功执行；需要 reauth 时流程可用，删除后本地数据清空并返回 Profile Sign-In。
2. Apple/Google 登录：
   - Edit Profile：允许更新 displayName/photoURL（若受限则 UI 需明确禁用说明）。
   - Update Email：按 Firebase 规范执行；若受限则禁用并说明；允许发送验证邮件至当前邮箱。
   - Change Password：入口禁用并显示说明；提供 `Auth.auth().sendPasswordReset(withEmail:)`。
   - Delete Account：触发系统 reauth 并完成删除。
3. 匿名用户：Profile → Account 仅显示 Sign In CTA；隐藏 Edit/Update/Change/Delete/Reset。
4. 离线：所有需要网络的 Firebase 操作禁用并提示；Reset Account 正常执行。
5. Telemetry：上述事件均被采集，包含 provider、result、error_code、requires_recent_login、language_code 等字段。
6. 回归 Sign Out：确认仍然有效并在 Profile → Account 内显示。

## 时间评估 & 里程碑（建议）
- 设计确认：1d
- 视图实现：2d
- 服务层与数据清理：2-3d
- QA & 回归：1-2d

## 已确认决策
1. Reset Account 执行时需同步删除 Firestore 中的 Quick History/Favorites 数据（待实现），并继续进行本地与偏好清理。
2. Delete Account 流程无需额外导出提示，用户确认后直接删除账号及本地数据。
3. Telemetry 事件命名必须对齐现有规范：与 Analytics 团队确认统一前缀（如 `account_*`）、动作动词及结果字段，确保埋点可被自动归类。
4. App 强制登录；匿名或未登录用户无法进入 Profile → Account 区域，也无需展示受限 CTA。

