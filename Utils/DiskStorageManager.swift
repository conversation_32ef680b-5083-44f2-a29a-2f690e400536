import Foundation

/// Protocol to abstract JSON-backed disk storage and binary cache with TTL.
public protocol DiskStorageProtocol: Sendable {
    func writeJSON<T: Codable>(_ value: T, forKey key: String, inFolder folder: String) throws
    func readJSON<T: Codable>(forKey key: String, inFolder folder: String, as type: T.Type) throws -> T
    func delete(forKey key: String, inFolder folder: String)

    func saveData(_ data: Data, forKey key: String, inFolder folder: String, ttl: TimeInterval)
    func loadData(forKey key: String, inFolder folder: String, ttl: TimeInterval) -> Data?
    func cleanupExpired(inFolder folder: String, ttl: TimeInterval)
}

/// File-backed implementation for JSON and data blobs with TTL support (for images or large payloads).
public final class DiskStorageManager: DiskStorageProtocol, @unchecked Sendable {
    public static let shared = DiskStorageManager()

    private let fm = FileManager.default
    private let jsonEncoder = StorageConfiguration.jsonEncoder
    private let jsonDecoder = StorageConfiguration.jsonDecoder

    // Use Application Support to persist across app sessions and avoid system purges.
    private lazy var baseDir: URL = {
        let urls = fm.urls(for: .applicationSupportDirectory, in: .userDomainMask)
        let base = urls.first!.appendingPathComponent("IngredientScanner", isDirectory: true)
        try? fm.createDirectory(at: base, withIntermediateDirectories: true)
        return base
    }()

    private init() {}

    // MARK: - JSON
    public func writeJSON<T: Codable>(_ value: T, forKey key: String, inFolder folder: String) throws {
        let url = fileURL(forKey: key, inFolder: folder, ext: "json")
        let data = try jsonEncoder.encode(value)
        try ensureFolderExists(folder: folder)
        try data.write(to: url, options: [.atomic])
    }

    public func readJSON<T: Codable>(forKey key: String, inFolder folder: String, as type: T.Type) throws -> T {
        let url = fileURL(forKey: key, inFolder: folder, ext: "json")
        let data = try Data(contentsOf: url)
        return try jsonDecoder.decode(T.self, from: data)
    }

    public func delete(forKey key: String, inFolder folder: String) {
        let url = fileURL(forKey: key, inFolder: folder, ext: "json")
        try? fm.removeItem(at: url)
    }

    // MARK: - Data with TTL (e.g., images)
    private struct DataEntry: Codable { let timestamp: TimeInterval; let payload: Data }

    public func saveData(_ data: Data, forKey key: String, inFolder folder: String, ttl: TimeInterval) {
        let entry = DataEntry(timestamp: Date().timeIntervalSince1970, payload: data)
        guard let box = try? jsonEncoder.encode(entry) else { return }
        let url = fileURL(forKey: key, inFolder: folder, ext: "bin")
        try? ensureFolderExists(folder: folder)
        try? box.write(to: url, options: [.atomic])
    }

    public func loadData(forKey key: String, inFolder folder: String, ttl: TimeInterval) -> Data? {
        let url = fileURL(forKey: key, inFolder: folder, ext: "bin")
        guard let box = try? Data(contentsOf: url),
              let entry = try? jsonDecoder.decode(DataEntry.self, from: box) else { return nil }
        let now = Date().timeIntervalSince1970
        if now - entry.timestamp > ttl {
            try? fm.removeItem(at: url)
            return nil
        }
        return entry.payload
    }

    public func cleanupExpired(inFolder folder: String, ttl: TimeInterval) {
        let dir = baseDir.appendingPathComponent(folder, isDirectory: true)
        guard let files = try? fm.contentsOfDirectory(at: dir, includingPropertiesForKeys: nil) else { return }
        let now = Date().timeIntervalSince1970
        for url in files where url.pathExtension == "bin" {
            if let box = try? Data(contentsOf: url),
               let entry = try? jsonDecoder.decode(DataEntry.self, from: box) {
                if now - entry.timestamp > ttl {
                    try? fm.removeItem(at: url)
                }
            }
        }
    }

    // MARK: - Helpers
    private func ensureFolderExists(folder: String) throws {
        let dir = baseDir.appendingPathComponent(folder, isDirectory: true)
        if !fm.fileExists(atPath: dir.path) {
            try fm.createDirectory(at: dir, withIntermediateDirectories: true)
        }
    }

    private func fileURL(forKey key: String, inFolder folder: String, ext: String) -> URL {
        let safeKey = key.replacingOccurrences(of: "/", with: "_")
        return baseDir
            .appendingPathComponent(folder, isDirectory: true)
            .appendingPathComponent(safeKey)
            .appendingPathExtension(ext)
    }
}

